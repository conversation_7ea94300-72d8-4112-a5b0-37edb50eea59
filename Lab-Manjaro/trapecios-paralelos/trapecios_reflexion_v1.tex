\documentclass[10pt,a4paper]{article}

%% paquetes básicos
\usepackage[utf8]{inputenc}
\usepackage[spanish]{babel}
\usepackage{a4wide,color,verbatim,Sweave,url,xargs,amsmath,booktabs,longtable}
\usepackage{graphicx,float}
\usepackage{tikz,xcolor}
\usepackage{enumitem}

%% bibliotecas TikZ
\usetikzlibrary{automata,positioning,calc,arrows}

%% entornos para exams
\newenvironment{question}{\item}{}
\newenvironment{solution}{\comment}{\endcomment}
\newenvironment{answerlist}{\renewcommand{\labelenumii}{(\alph{enumii})}\begin{enumerate}}{\end{enumerate}}



%% configuración párrafos
\setlength{\parskip}{0.7ex plus0.1ex minus0.1ex}
\setlength{\parindent}{0em}

\begin{document}
\input{trapecios_reflexion_v1-concordance}

\begin{enumerate}


\begin{question}

El trapecio isósceles WXYZ es la reflexión del trapecio isósceles UVKL respecto a la línea $n$, como muestra la figura.

\includegraphics[width=12cm]{trapecios_reflexion.png}
Comparando los trapecios, ¿qué lados son paralelos entre sí?

\begin{answerlist}
  \item VK y YX
  \item UV y WX
  \item UV y YZ
  \item XY y ZW
\end{answerlist}
\end{question}

\begin{solution}

En una reflexión respecto a una línea, los lados correspondientes que no son paralelos al eje de reflexión se mantienen paralelos entre sí.

En este caso, el eje $n$ es paralelo a los lados UV, KL, XY y ZW.

Por lo tanto, los lados que no son paralelos al eje de reflexión son los que se mantienen paralelos después de la transformación: UV y WX.

\begin{answerlist}
  \item Falso. Estos lados no corresponden a lados homólogos en la reflexión o son paralelos al eje.
  \item Verdadero. En una reflexión, los lados correspondientes que no son paralelos al eje de reflexión se mantienen paralelos entre sí.
  \item Falso. Estos lados no corresponden a lados homólogos en la reflexión o son paralelos al eje.
  \item Falso. Estos lados no corresponden a lados homólogos en la reflexión o son paralelos al eje.
\end{answerlist}
\end{solution}

%% META-INFORMATION
\exname{Trapecios Reflexión Paralelismo}
\extype{schoice}
\exsolution{0100}
\exshuffle{TRUE}
\exsection{Geometría Métrica}

\end{enumerate}
\end{document}
