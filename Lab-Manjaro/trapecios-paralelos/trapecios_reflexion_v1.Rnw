\documentclass[10pt,a4paper]{article}

%% paquetes básicos
\usepackage[utf8]{inputenc}
\usepackage[spanish]{babel}
\usepackage{a4wide,color,verbatim,Sweave,url,xargs,amsmath,booktabs,longtable}
\usepackage{graphicx,float}
\usepackage{tikz,xcolor}
\usepackage{enumitem}

%% bibliotecas TikZ
\usetikzlibrary{automata,positioning,calc,arrows}

%% entornos para exams
\newenvironment{question}{\item}{}
\newenvironment{solution}{\comment}{\endcomment}
\newenvironment{answerlist}{\renewcommand{\labelenumii}{(\alph{enumii})}\begin{enumerate}}{\end{enumerate}}



%% configuración párrafos
\setlength{\parskip}{0.7ex plus0.1ex minus0.1ex}
\setlength{\parindent}{0em}

\begin{document}
\SweaveOpts{concordance=TRUE}

\begin{enumerate}

<<echo=FALSE, results=hide>>=
# Configuración inicial
library(exams)
library(digest)
library(testthat)
library(knitr)

# Configuración TikZ
typ <- match_exams_device()
if(match_exams_call() == "exams2nops") typ <- "tex"

# Semilla aleatoria
set.seed(sample(1:100000, 1))

# Función generar_datos()
generar_datos <- function() {
  # Aleatorización de colores para los trapecios
  colores_trapecio1 <- sample(c("cyan!70", "blue!60", "teal!70", "blue!30"), 1)
  colores_trapecio2 <- sample(c("purple!70", "magenta!60", "violet!70", "red!60"), 1)

  # Aleatorización de nombres de vértices
  vertices_sets <- list(
    c("E", "F", "G", "H", "O", "P", "Q", "R"),
    c("A", "B", "C", "D", "M", "N", "S", "T"),
    c("W", "X", "Y", "Z", "U", "V", "K", "L"),
    c("I", "J", "L", "M", "P", "Q", "R", "S")
  )
  vertices <- sample(vertices_sets, 1)[[1]]

  # Asignar vértices a cada trapecio
  trap1_vertices <- vertices[1:4]  # Primer trapecio
  trap2_vertices <- vertices[5:8]  # Segundo trapecio

  # Aleatorización del eje de reflexión
  eje_nombre <- sample(c("k", "m", "n", "r"), 1)

  # Generar opciones de respuesta
  # La respuesta correcta son los lados no paralelos al eje
  respuesta_correcta <- paste(trap2_vertices[1], trap2_vertices[2], " y ",
                             trap1_vertices[1], trap1_vertices[2], sep="")

  # Distractores
  distractor1 <- paste(trap1_vertices[2], trap1_vertices[3], " y ",
                      trap1_vertices[4], trap1_vertices[1], sep="")
  distractor2 <- paste(trap2_vertices[1], trap2_vertices[2], " y ",
                      trap1_vertices[3], trap1_vertices[4], sep="")
  distractor3 <- paste(trap2_vertices[2], trap2_vertices[3], " y ",
                      trap1_vertices[3], trap1_vertices[2], sep="")

  opciones <- c(respuesta_correcta, distractor1, distractor2, distractor3)
  opciones <- sample(opciones)

  # Determinar cuál es la correcta
  solutions <- opciones == respuesta_correcta

  # Explicaciones para answerlist
  explicaciones <- ifelse(solutions,
    "Verdadero. En una reflexión, los lados correspondientes que no son paralelos al eje de reflexión se mantienen paralelos entre sí.",
    "Falso. Estos lados no corresponden a lados homólogos en la reflexión o son paralelos al eje.")

  return(list(
    color1 = colores_trapecio1,
    color2 = colores_trapecio2,
    vertices1 = trap1_vertices,
    vertices2 = trap2_vertices,
    eje = eje_nombre,
    opciones = opciones,
    solutions = solutions,
    explicaciones = explicaciones,
    respuesta_correcta = respuesta_correcta
  ))
}

# Generar datos para este ejercicio
datos <- generar_datos()

# Función para crear el diagrama TikZ
crear_diagrama_trapecios <- function(datos) {
  tikz_code <- c(
    "\\begin{tikzpicture}[scale=1.2]",
    "% Definir coordenadas para el primer trapecio (superior izquierdo)",
    paste0("\\coordinate (", datos$vertices1[1], ") at (-2, 2);"),
    paste0("\\coordinate (", datos$vertices1[2], ") at (-0.5, 2);"),
    paste0("\\coordinate (", datos$vertices1[3], ") at (0, 1);"),
    paste0("\\coordinate (", datos$vertices1[4], ") at (-2.5, 1);"),
    "",
    "% Definir coordenadas para el segundo trapecio (inferior derecho)",
    paste0("\\coordinate (", datos$vertices2[1], ") at (1, -1);"),
    paste0("\\coordinate (", datos$vertices2[2], ") at (2.5, -1);"),
    paste0("\\coordinate (", datos$vertices2[3], ") at (2, -2);"),
    paste0("\\coordinate (", datos$vertices2[4], ") at (0.5, -2);"),
    "",
    "% Dibujar primer trapecio",
    paste0("\\fill[", datos$color1, "] (", datos$vertices1[1], ") -- (",
           datos$vertices1[2], ") -- (", datos$vertices1[3], ") -- (",
           datos$vertices1[4], ") -- cycle;"),
    paste0("\\draw[thick] (", datos$vertices1[1], ") -- (",
           datos$vertices1[2], ") -- (", datos$vertices1[3], ") -- (",
           datos$vertices1[4], ") -- cycle;"),
    "",
    "% Dibujar segundo trapecio",
    paste0("\\fill[", datos$color2, "] (", datos$vertices2[1], ") -- (",
           datos$vertices2[2], ") -- (", datos$vertices2[3], ") -- (",
           datos$vertices2[4], ") -- cycle;"),
    paste0("\\draw[thick] (", datos$vertices2[1], ") -- (",
           datos$vertices2[2], ") -- (", datos$vertices2[3], ") -- (",
           datos$vertices2[4], ") -- cycle;"),
    "",
    "% Línea de reflexión",
    paste0("\\draw[thick, black] (-3, 3) -- (3, -3) node[right] {$", datos$eje, "$};"),
    "",
    "% Etiquetas de vértices primer trapecio",
    paste0("\\node[above left] at (", datos$vertices1[1], ") {$", datos$vertices1[1], "$};"),
    paste0("\\node[above right] at (", datos$vertices1[2], ") {$", datos$vertices1[2], "$};"),
    paste0("\\node[below right] at (", datos$vertices1[3], ") {$", datos$vertices1[3], "$};"),
    paste0("\\node[below left] at (", datos$vertices1[4], ") {$", datos$vertices1[4], "$};"),
    "",
    "% Etiquetas de vértices segundo trapecio",
    paste0("\\node[above left] at (", datos$vertices2[1], ") {$", datos$vertices2[1], "$};"),
    paste0("\\node[above right] at (", datos$vertices2[2], ") {$", datos$vertices2[2], "$};"),
    paste0("\\node[below right] at (", datos$vertices2[3], ") {$", datos$vertices2[3], "$};"),
    paste0("\\node[below left] at (", datos$vertices2[4], ") {$", datos$vertices2[4], "$};"),
    "",
    "% Texto explicativo",
    paste0("\\node[right, text width=4cm] at (4, 0) {El eje $", datos$eje,
           "$ es paralelo a los lados $", datos$vertices2[1], datos$vertices2[2],
           "$, $", datos$vertices2[3], datos$vertices2[4], "$, $",
           datos$vertices1[2], datos$vertices1[3], "$ y $",
           datos$vertices1[4], datos$vertices1[1], "$};"),
    "\\end{tikzpicture}"
  )
  return(tikz_code)
}

# Crear el código TikZ
codigo_tikz <- crear_diagrama_trapecios(datos)
@

\begin{question}

El trapecio isósceles \Sexpr{paste(datos$vertices1, collapse="")} es la reflexión del trapecio isósceles \Sexpr{paste(datos$vertices2, collapse="")} respecto a la línea $\Sexpr{datos$eje}$, como muestra la figura.

<<echo=FALSE, results=tex>>=
include_tikz(codigo_tikz, name = "trapecios_reflexion", format = typ,
  library = c("positioning", "calc"),
  width = "12cm")
@

Comparando los trapecios, ¿qué lados son paralelos entre sí?

<<echo=FALSE, results=tex>>=
answerlist(datos$opciones)
@

\end{question}

\begin{solution}

En una reflexión respecto a una línea, los lados correspondientes que no son paralelos al eje de reflexión se mantienen paralelos entre sí.

En este caso, el eje $\Sexpr{datos$eje}$ es paralelo a los lados \Sexpr{paste(datos$vertices2[1], datos$vertices2[2], sep="")}, \Sexpr{paste(datos$vertices2[3], datos$vertices2[4], sep="")}, \Sexpr{paste(datos$vertices1[2], datos$vertices1[3], sep="")} y \Sexpr{paste(datos$vertices1[4], datos$vertices1[1], sep="")}.

Por lo tanto, los lados que no son paralelos al eje de reflexión son los que se mantienen paralelos después de la transformación: \Sexpr{datos$respuesta_correcta}.

<<echo=FALSE, results=tex>>=
answerlist(datos$explicaciones)
@

\end{solution}

%% META-INFORMATION
\exname{Trapecios Reflexión Paralelismo}
\extype{schoice}
\exsolution{\Sexpr{mchoice2string(datos$solutions)}}
\exshuffle{TRUE}
\exsection{Geometría Métrica}

\end{enumerate}
\end{document}
